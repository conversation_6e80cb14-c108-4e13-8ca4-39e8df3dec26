{"rustc": 16591470773350601817, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[1663413536764493210, "build_script_build", false, 1290822852292120509], [12092653563678505622, "build_script_build", false, 12751230808833058268], [16702348383442838006, "build_script_build", false, 165103089684905920]], "local": [{"RerunIfChanged": {"output": "debug\\build\\nookat-f3a454b2d2fe264a\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}