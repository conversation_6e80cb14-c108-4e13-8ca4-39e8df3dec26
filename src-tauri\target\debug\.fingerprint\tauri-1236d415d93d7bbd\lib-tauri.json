{"rustc": 16591470773350601817, "features": "[\"common-controls-v6\", \"compression\", \"default\", \"dynamic-acl\", \"macos-private-api\", \"tauri-runtime-wry\", \"webkit2gtk\", \"webview2-com\", \"wry\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"dynamic-acl\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\", \"x11\"]", "target": 12223948975794516716, "profile": 2241668132362809309, "path": 2958595331794903355, "deps": [[40386456601120721, "percent_encoding", false, 3036556584864954131], [654232091421095663, "tauri_utils", false, 5768639461870865419], [1200537532907108615, "url<PERSON><PERSON>n", false, 4648726796997381990], [2013030631243296465, "webview2_com", false, 142183234344881076], [3150220818285335163, "url", false, 7777538485070522714], [3331586631144870129, "getrandom", false, 544040876074508077], [4143744114649553716, "raw_window_handle", false, 12539634287702608233], [4494683389616423722, "muda", false, 15486018325334414546], [4919829919303820331, "serialize_to_javascript", false, 6756657341990704806], [5986029879202738730, "log", false, 3933214718944168392], [8569119365930580996, "serde_json", false, 4125861822706750416], [9010263965687315507, "http", false, 18074761769288440948], [9689903380558560274, "serde", false, 1763213709428686712], [10229185211513642314, "mime", false, 6953617305406070350], [10806645703491011684, "thiserror", false, 14246692844772049497], [11989259058781683633, "dunce", false, 18257853716818077373], [12092653563678505622, "build_script_build", false, 12751230808833058268], [12304025191202589669, "tauri_runtime_wry", false, 6005014604417741102], [12393800526703971956, "tokio", false, 3878676366054311607], [12565293087094287914, "window_vibrancy", false, 12596588433568561930], [12943761728066819757, "tauri_runtime", false, 12708943681671317763], [12986574360607194341, "serde_repr", false, 5237414082616257837], [13077543566650298139, "heck", false, 6230302215676125376], [13405681745520956630, "tauri_macros", false, 10774614060632485978], [13625485746686963219, "anyhow", false, 12089293014055307075], [14585479307175734061, "windows", false, 4340503289396432750], [16928111194414003569, "dirs", false, 3317782112780410193], [17155886227862585100, "glob", false, 10366613398989513517]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-1236d415d93d7bbd\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}