["\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\window\\autogenerated\\commands\\available_monitors.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\window\\autogenerated\\commands\\center.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\window\\autogenerated\\commands\\close.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\window\\autogenerated\\commands\\create.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\window\\autogenerated\\commands\\current_monitor.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\window\\autogenerated\\commands\\cursor_position.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\window\\autogenerated\\commands\\destroy.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\window\\autogenerated\\commands\\get_all_windows.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\window\\autogenerated\\commands\\hide.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\window\\autogenerated\\commands\\inner_position.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\window\\autogenerated\\commands\\inner_size.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\window\\autogenerated\\commands\\internal_toggle_maximize.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\window\\autogenerated\\commands\\is_always_on_top.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\window\\autogenerated\\commands\\is_closable.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\window\\autogenerated\\commands\\is_decorated.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\window\\autogenerated\\commands\\is_enabled.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\window\\autogenerated\\commands\\is_focused.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\window\\autogenerated\\commands\\is_fullscreen.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\window\\autogenerated\\commands\\is_maximizable.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\window\\autogenerated\\commands\\is_maximized.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\window\\autogenerated\\commands\\is_minimizable.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\window\\autogenerated\\commands\\is_minimized.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\window\\autogenerated\\commands\\is_resizable.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\window\\autogenerated\\commands\\is_visible.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\window\\autogenerated\\commands\\maximize.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\window\\autogenerated\\commands\\minimize.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\window\\autogenerated\\commands\\monitor_from_point.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\window\\autogenerated\\commands\\outer_position.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\window\\autogenerated\\commands\\outer_size.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\window\\autogenerated\\commands\\primary_monitor.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\window\\autogenerated\\commands\\request_user_attention.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\window\\autogenerated\\commands\\scale_factor.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\window\\autogenerated\\commands\\set_always_on_bottom.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\window\\autogenerated\\commands\\set_always_on_top.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\window\\autogenerated\\commands\\set_background_color.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\window\\autogenerated\\commands\\set_badge_count.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\window\\autogenerated\\commands\\set_badge_label.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\window\\autogenerated\\commands\\set_closable.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\window\\autogenerated\\commands\\set_content_protected.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\window\\autogenerated\\commands\\set_cursor_grab.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\window\\autogenerated\\commands\\set_cursor_icon.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\window\\autogenerated\\commands\\set_cursor_position.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\window\\autogenerated\\commands\\set_cursor_visible.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\window\\autogenerated\\commands\\set_decorations.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\window\\autogenerated\\commands\\set_effects.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\window\\autogenerated\\commands\\set_enabled.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\window\\autogenerated\\commands\\set_focus.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\window\\autogenerated\\commands\\set_fullscreen.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\window\\autogenerated\\commands\\set_icon.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\window\\autogenerated\\commands\\set_ignore_cursor_events.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\window\\autogenerated\\commands\\set_max_size.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\window\\autogenerated\\commands\\set_maximizable.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\window\\autogenerated\\commands\\set_min_size.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\window\\autogenerated\\commands\\set_minimizable.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\window\\autogenerated\\commands\\set_overlay_icon.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\window\\autogenerated\\commands\\set_position.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\window\\autogenerated\\commands\\set_progress_bar.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\window\\autogenerated\\commands\\set_resizable.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\window\\autogenerated\\commands\\set_shadow.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\window\\autogenerated\\commands\\set_size.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\window\\autogenerated\\commands\\set_size_constraints.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\window\\autogenerated\\commands\\set_skip_taskbar.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\window\\autogenerated\\commands\\set_theme.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\window\\autogenerated\\commands\\set_title.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\window\\autogenerated\\commands\\set_title_bar_style.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\window\\autogenerated\\commands\\set_visible_on_all_workspaces.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\window\\autogenerated\\commands\\show.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\window\\autogenerated\\commands\\start_dragging.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\window\\autogenerated\\commands\\start_resize_dragging.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\window\\autogenerated\\commands\\theme.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\window\\autogenerated\\commands\\title.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\window\\autogenerated\\commands\\toggle_maximize.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\window\\autogenerated\\commands\\unmaximize.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\window\\autogenerated\\commands\\unminimize.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\window\\autogenerated\\default.toml"]