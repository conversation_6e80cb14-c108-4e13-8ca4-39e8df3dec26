["\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\path\\autogenerated\\commands\\basename.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\path\\autogenerated\\commands\\dirname.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\path\\autogenerated\\commands\\extname.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\path\\autogenerated\\commands\\is_absolute.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\path\\autogenerated\\commands\\join.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\path\\autogenerated\\commands\\normalize.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\path\\autogenerated\\commands\\resolve.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\path\\autogenerated\\commands\\resolve_directory.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\path\\autogenerated\\default.toml"]