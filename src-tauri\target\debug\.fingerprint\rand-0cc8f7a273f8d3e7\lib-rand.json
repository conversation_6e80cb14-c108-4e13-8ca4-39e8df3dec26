{"rustc": 16591470773350601817, "features": "[\"alloc\", \"default\", \"getrandom\", \"getrandom_package\", \"libc\", \"rand_pcg\", \"small_rng\", \"std\"]", "declared_features": "[\"alloc\", \"default\", \"getrandom\", \"getrandom_package\", \"libc\", \"log\", \"nightly\", \"packed_simd\", \"rand_pcg\", \"serde1\", \"simd_support\", \"small_rng\", \"std\", \"stdweb\", \"wasm-bindgen\"]", "target": 8827111241893198906, "profile": 2225463790103693989, "path": 12634623645338545735, "deps": [[1333041802001714747, "rand_chacha", false, 3365376326160439313], [1740877332521282793, "rand_core", false, 6067626114077567855], [5170503507811329045, "getrandom_package", false, 15054216513051467195], [9875507072765444643, "rand_pcg", false, 10733433857952746919]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\rand-0cc8f7a273f8d3e7\\dep-lib-rand", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}