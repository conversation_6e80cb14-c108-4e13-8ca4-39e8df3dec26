["\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\menu\\autogenerated\\commands\\append.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\menu\\autogenerated\\commands\\create_default.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\menu\\autogenerated\\commands\\get.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\menu\\autogenerated\\commands\\insert.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\menu\\autogenerated\\commands\\is_checked.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\menu\\autogenerated\\commands\\is_enabled.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\menu\\autogenerated\\commands\\items.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\menu\\autogenerated\\commands\\new.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\menu\\autogenerated\\commands\\popup.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\menu\\autogenerated\\commands\\prepend.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\menu\\autogenerated\\commands\\remove.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\menu\\autogenerated\\commands\\remove_at.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\menu\\autogenerated\\commands\\set_accelerator.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\menu\\autogenerated\\commands\\set_as_app_menu.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\menu\\autogenerated\\commands\\set_as_help_menu_for_nsapp.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\menu\\autogenerated\\commands\\set_as_window_menu.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\menu\\autogenerated\\commands\\set_as_windows_menu_for_nsapp.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\menu\\autogenerated\\commands\\set_checked.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\menu\\autogenerated\\commands\\set_enabled.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\menu\\autogenerated\\commands\\set_icon.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\menu\\autogenerated\\commands\\set_text.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\menu\\autogenerated\\commands\\text.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\menu\\autogenerated\\default.toml"]