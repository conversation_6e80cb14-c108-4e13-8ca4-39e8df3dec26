["\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\app\\autogenerated\\commands\\app_hide.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\app\\autogenerated\\commands\\app_show.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\app\\autogenerated\\commands\\bundle_type.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\app\\autogenerated\\commands\\default_window_icon.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\app\\autogenerated\\commands\\fetch_data_store_identifiers.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\app\\autogenerated\\commands\\identifier.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\app\\autogenerated\\commands\\name.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\app\\autogenerated\\commands\\remove_data_store.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\app\\autogenerated\\commands\\set_app_theme.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\app\\autogenerated\\commands\\set_dock_visibility.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\app\\autogenerated\\commands\\tauri_version.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\app\\autogenerated\\commands\\version.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\app\\autogenerated\\default.toml"]