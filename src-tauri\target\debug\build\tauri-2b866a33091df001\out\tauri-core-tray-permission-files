["\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\tray\\autogenerated\\commands\\get_by_id.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\tray\\autogenerated\\commands\\new.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\tray\\autogenerated\\commands\\remove_by_id.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\tray\\autogenerated\\commands\\set_icon.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\tray\\autogenerated\\commands\\set_icon_as_template.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\tray\\autogenerated\\commands\\set_menu.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\tray\\autogenerated\\commands\\set_show_menu_on_left_click.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\tray\\autogenerated\\commands\\set_temp_dir_path.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\tray\\autogenerated\\commands\\set_title.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\tray\\autogenerated\\commands\\set_tooltip.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\tray\\autogenerated\\commands\\set_visible.toml", "\\\\?\\C:\\52770\\nookat\\src-tauri\\target\\debug\\build\\tauri-2b866a33091df001\\out\\permissions\\tray\\autogenerated\\default.toml"]