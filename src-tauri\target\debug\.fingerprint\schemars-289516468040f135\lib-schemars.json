{"rustc": 16591470773350601817, "features": "[\"default\", \"derive\", \"indexmap\", \"preserve_order\", \"schemars_derive\", \"url\", \"uuid1\"]", "declared_features": "[\"arrayvec\", \"arrayvec05\", \"arrayvec07\", \"bigdecimal\", \"bigdecimal03\", \"bigdecimal04\", \"bytes\", \"chrono\", \"default\", \"derive\", \"derive_json_schema\", \"either\", \"enumset\", \"impl_json_schema\", \"indexmap\", \"indexmap1\", \"indexmap2\", \"preserve_order\", \"raw_value\", \"rust_decimal\", \"schemars_derive\", \"semver\", \"smallvec\", \"smol_str\", \"ui_test\", \"url\", \"uuid\", \"uuid08\", \"uuid1\"]", "target": 11155677158530064643, "profile": 2225463790103693989, "path": 9954005431892102951, "deps": [[3150220818285335163, "url", false, 6453596587757033593], [6913375703034175521, "build_script_build", false, 7652472617764511933], [8319709847752024821, "uuid1", false, 14042345540125855114], [8569119365930580996, "serde_json", false, 5188641222359304286], [9122563107207267705, "dyn_clone", false, 1245482669511514747], [9689903380558560274, "serde", false, 10307653461189928879], [14923790796823607459, "indexmap", false, 2145877156545605570], [16071897500792579091, "schemars_derive", false, 3682080564389757736]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\schemars-289516468040f135\\dep-lib-schemars", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}