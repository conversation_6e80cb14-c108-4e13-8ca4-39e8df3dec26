{"default": {"identifier": "default", "description": "Capability for the main window", "local": true, "windows": ["main"], "permissions": ["core:default", "opener:default", "core:app:allow-app-hide", "core:app:allow-app-show", "core:app:allow-default-window-icon", "core:app:allow-name", "core:app:allow-set-app-theme", "core:app:allow-tauri-version", "core:app:allow-version", "core:event:allow-emit", "core:event:allow-emit-to", "core:event:allow-listen", "core:event:allow-unlisten", "core:menu:allow-append", "core:menu:allow-create-default", "core:menu:allow-get", "core:menu:allow-insert", "core:menu:allow-is-checked", "core:menu:allow-is-enabled", "core:menu:allow-items", "core:menu:allow-new", "core:menu:allow-popup", "core:menu:allow-prepend", "core:menu:allow-remove", "core:menu:allow-remove-at", "core:menu:allow-set-accelerator", "core:menu:allow-set-as-app-menu", "core:menu:allow-set-as-help-menu-for-nsapp", "core:menu:allow-set-as-window-menu", "core:menu:allow-set-as-windows-menu-for-nsapp", "core:menu:allow-set-checked", "core:menu:allow-set-enabled", "core:menu:allow-set-icon", "core:menu:allow-set-text", "core:menu:allow-text", "core:tray:allow-get-by-id", "core:tray:allow-new", "core:tray:allow-remove-by-id", "core:tray:allow-set-icon", "core:tray:allow-set-icon-as-template", "core:tray:allow-set-menu", "core:tray:allow-set-show-menu-on-left-click", "core:tray:allow-set-temp-dir-path", "core:tray:allow-set-title", "core:tray:allow-set-tooltip", "core:tray:allow-set-visible", "core:window:allow-available-monitors", "core:window:allow-center", "core:window:allow-close", "core:window:allow-create", "core:window:allow-current-monitor", "core:window:allow-cursor-position", "core:window:allow-destroy", "core:window:allow-get-all-windows", "core:window:allow-hide", "core:window:allow-inner-position", "core:window:allow-inner-size", "core:window:allow-internal-toggle-maximize", "core:window:allow-is-closable", "core:window:allow-is-decorated", "core:window:allow-is-enabled", "core:window:allow-is-focused", "core:window:allow-is-fullscreen", "core:window:allow-is-maximizable", "core:window:allow-is-maximized", "core:window:allow-is-minimizable", "core:window:allow-is-minimized", "core:window:allow-is-resizable", "core:window:allow-is-visible", "core:window:allow-maximize", "core:window:allow-minimize", "core:window:allow-monitor-from-point", "core:window:allow-outer-position", "core:window:allow-outer-size", "core:window:allow-primary-monitor", "core:window:allow-request-user-attention", "core:window:allow-scale-factor", "core:window:allow-set-always-on-bottom", "core:window:allow-set-always-on-top", "core:window:allow-set-closable", "core:window:allow-set-content-protected", "core:window:allow-set-cursor-grab", "core:window:allow-set-cursor-icon", "core:window:allow-set-cursor-position", "core:window:allow-set-cursor-visible", "core:window:allow-set-decorations", "core:window:allow-set-effects", "core:window:allow-set-enabled", "core:window:allow-set-focus", "core:window:allow-set-fullscreen", "core:window:allow-set-icon", "core:window:allow-set-ignore-cursor-events", "core:window:allow-set-max-size", "core:window:allow-set-maximizable", "core:window:allow-set-min-size", "core:window:allow-set-minimizable", "core:window:allow-set-position", "core:window:allow-set-progress-bar", "core:window:allow-set-resizable", "core:window:allow-set-shadow", "core:window:allow-set-size", "core:window:allow-set-size-constraints", "core:window:allow-set-skip-taskbar", "core:window:allow-set-theme", "core:window:allow-set-title", "core:window:allow-set-title-bar-style", "core:window:allow-set-visible-on-all-workspaces", "core:window:allow-show", "core:window:allow-start-dragging", "core:window:allow-start-resize-dragging", "core:window:allow-theme", "core:window:allow-title", "core:window:allow-toggle-maximize", "core:window:allow-unmaximize", "core:window:allow-unminimize", "core:app:default", "core:event:default", "core:image:default", "core:menu:default", "core:path:default", "core:resources:default", "core:tray:default", "core:webview:default", "core:window:default"]}}