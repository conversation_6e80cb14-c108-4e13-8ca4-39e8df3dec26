{"rustc": 16591470773350601817, "features": "[\"compression\"]", "declared_features": "[\"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"isolation\", \"tracing\"]", "target": 4649449654257170297, "profile": 2225463790103693989, "path": 14267277207431836455, "deps": [[654232091421095663, "tauri_utils", false, 12831554999449118254], [2704937418414716471, "tauri_codegen", false, 3886418566782076052], [3060637413840920116, "proc_macro2", false, 4420035611467293243], [4974441333307933176, "syn", false, 8981830137992093136], [13077543566650298139, "heck", false, 1633659506774498337], [17990358020177143287, "quote", false, 10960193892747019081]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-macros-f9f64eaf1879fa5e\\dep-lib-tauri_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}