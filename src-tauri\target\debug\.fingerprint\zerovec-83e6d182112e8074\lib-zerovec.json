{"rustc": 16591470773350601817, "features": "[\"alloc\", \"derive\", \"yoke\"]", "declared_features": "[\"alloc\", \"databake\", \"derive\", \"hashmap\", \"serde\", \"std\", \"yoke\"]", "target": 1825474209729987087, "profile": 15657897354478470176, "path": 5460996130027165835, "deps": [[9620753569207166497, "zerovec_derive", false, 10480667519184980910], [10706449961930108323, "yoke", false, 12222666680166017308], [17046516144589451410, "zerofrom", false, 3750800763365153561]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\zerovec-83e6d182112e8074\\dep-lib-zerovec", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}